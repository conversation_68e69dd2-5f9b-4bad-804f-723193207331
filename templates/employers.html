{% extends "base.html" %} {% block title %}Employers{% endblock %} {% block
content %}
<div class="less-limited-width-content px-3">
  <div class="container-fluid py-4">
    <h1 class="display-5 fw-bold mb-4 text-primary">Explore Employers</h1>
    <p class="lead mb-5">
      Discover innovative organizations shaping the future of work
    </p>

    <div id="search-div" class="rounded p-3 mb-4">
      <input
        class="form-control"
        id="keyword-input"
        name="keyword-input"
        placeholder="Search companies..."
        hx-get="/filteremp"
        hx-target="#employers-list"
        hx-trigger="keyup changed delay:300ms"
      />
    </div>

    <div id="employers-list" class="row g-3">
      {% for employer in all_employers %}
      <div class="col-xl-4 col-lg-6 col-md-6 col-12">
        <div class="employer-card">
          <div class="company-logo-wrapper">
            <div class="d-flex justify-content-between align-items-center">
              <img
                src="{{ employer.employer_logo_url }}"
                class="employer-icon-md"
                alt="{{ employer.employer_name }}"
              />
              <div class="open-positions">
                {{ employer.open_positions }} Openings
              </div>
            </div>
          </div>

          <div class="company-details">
            <h6 class="fw-bold mb-3">{{ employer.employer_name }}</h6>

            <div class="detail-item">
              <span class="detail-label">Industry</span>
              <span class="detail-value">{{ employer.employer_industry }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Employees</span>
              <span class="detail-value">{{ employer.employer_headcount }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">Location</span>
              <span class="detail-value">{{ employer.headquarter }}</span>
            </div>
          </div>

          <a
            href="{{url_for('employer',employer_id=employer.employer_id)}}"
            class="btn btn-outline-primary w-100 py-2 border-top rounded-0"
          >
            View Details →
          </a>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</div>
{% endblock %}
