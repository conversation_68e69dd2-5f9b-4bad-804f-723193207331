{% extends "base.html" %} {% block title %}Join Talent Pool{% endblock %}

<script>
  function validateFileSize(input) {
    const file = input.files[0];
    if (file) {
      const fileSizeInMB = file.size / 1024 / 1024;
      if (fileSizeInMB > 3) {
        alert("File size must be less than 3MB");
        input.value = ""; // Clear the input field
      } else if (file.type !== "application/pdf") {
        alert("File must be a PDF");
        input.value = ""; // Clear the input field
      }
    }
  }
</script>

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center align-items-center">
    <div class="col-12 col-md-8 col-lg-6 d-none d-lg-block">
      <div class="rounded p-4 mb-4">
        <img
          src="./static/MessyDoodle.svg"
          alt="placeholder"
          class="img-fluid rounded mx-auto d-block"
          style="max-height: 300px"
        />
      </div>
    </div>

    <div class="col-12 col-md-8 col-lg-6">
      <div class="rounded shadow p-4 mb-4" style="background-color: white">
        <h2 class="text-center mb-4">Join Our Talent Pool</h2>
        <p class="text-center text-muted mb-4">
          Register once and get contacted by employers directly when matching
          opportunities arise
        </p>

        <form action="/talent-pool" method="post" enctype="multipart/form-data">
          <!-- Name and Surname Input -->
          <div class="row mb-4">
            <div class="col-6">
              <label for="firstName" class="form-label">First Name</label>
              <input
                type="text"
                class="form-control"
                id="firstName"
                name="firstName"
                placeholder="John"
                required
              />
            </div>
            <div class="col-6">
              <label for="lastName" class="form-label">Last Name</label>
              <input
                type="text"
                class="form-control"
                id="lastName"
                name="lastName"
                placeholder="Doe"
                required
              />
            </div>
          </div>

          <!-- Phone Input -->
          <div class="mb-4">
            <label for="phone" class="form-label">Phone (Optional)</label>
            <input
              type="tel"
              class="form-control"
              id="phone"
              name="phone"
              placeholder="+****************"
            />
          </div>
            <!-- Location Input -->
            <div class="mb-4">
            <label for="location" class="form-label">Country of Residence</label>
            <select
              class="form-select"
              id="location"
              name="location"
              required
            >
              <option value="" disabled selected>Select your country</option>
            </select>
            </div>

            <script>
            document.addEventListener("DOMContentLoaded", function () {
              const countryDropdown = document.getElementById("location");
              fetch("https://restcountries.com/v3.1/all")
              .then((response) => response.json())
              .then((countries) => {
                const sortedCountries = countries.sort((a, b) =>
                a.name.common.localeCompare(b.name.common)
                );
                sortedCountries.forEach((country) => {
                const option = document.createElement("option");
                option.value = country.name.common;
                option.textContent = country.name.common;
                countryDropdown.appendChild(option);
                });
              })
              .catch((error) => {
                console.error("Error fetching countries:", error);
              });
            });
            </script>

          <!-- Email Input -->
          <div class="mb-4">
            <label for="email" class="form-label">Email</label>
            <input
              type="email"
              class="form-control"
              id="email"
              name="email"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <!-- CV Upload -->
          <div class="mb-4">
            <label for="cvUpload" class="form-label">Upload Your CV</label>
            <input
              type="file"
              class="form-control"
              id="cvUpload"
              name="cvUpload"
              accept=".pdf"
              onchange="validateFileSize(this)"
              required
            />
            <div class="form-text">PDF format only (max 3MB)</div>
          </div>

          <!-- Submit Button -->
          <div class="d-grid gap-2">
            <button type="submit" class="btn btn-primary btn-lg">
              Join Talent Pool
            </button>
          </div>
        </form>

        <div class="text-center mt-4">
          <small class="text-muted">
            By registering, you agree to be contacted by verified employers
          </small>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
