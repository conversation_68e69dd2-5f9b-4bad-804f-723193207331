{% extends "base.html" %} 
{% block title %} Jobsvider {% endblock %} 
{% block content%}

<!-- Hero Section -->
<section class="gradient-bg py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8 order-lg-1 order-2">
        <h1 class="display-4 fw-bold mb-4">
          Find Your Perfect<br />
          <span class="text-primary">Career Opportunity</span>
        </h1>
        <p class="lead text-muted mb-4">
          Discover top companies, explore ideal positions, and ensure your
          workplace expectations are met. Join our talent pool to receive
          curated offers from leading employers.
        </p>
        <div class="d-flex flex-wrap gap-3 mb-4">
          <a href="/jobs" class="btn btn-primary btn-lg px-4">
            Browse Jobs <i class="bi bi-search ms-2"></i>
          </a>
          <a href="/employers" class="btn btn-outline-primary btn-lg px-4">
            View Companies <i class="bi bi-building ms-2"></i>
          </a>
        </div>
        <p class="text-muted">
          Looking to hire?
          <a
            href="/employer-signup"
            class="text-decoration-none fw-semibold text-primary"
          >
            Create Employer Profile <i class="bi bi-arrow-right-short"></i>
          </a>
        </p>
      </div>
      <div class="col-lg-4 order-lg-2 order-1 mb-4 mb-lg-0 px-auto">
        <img
          src="./static/hero-icon.svg"
          alt="Career opportunities"
          class="img-fluid rounded mx-auto d-block hero-img"
        />
      </div>
    </div>
  </div>
</section>

<!-- Recent Remote Jobs Section -->
<section class="py-5">
  <div class="container">
    <h2 class="fs-1 fw-bold border-bottom pb-3 mb-4">
      <i class="bi bi-laptop me-3"></i>
      Recent Remote Opportunities
    </h2>

    <div class="row g-4">
      {% for vacancy in recent_remote_jobs %}
      <a
        href="{{ url_for('view_vacancy', vacancy_id=vacancy.vacancy_id) }}"
        class="col-md-6 text-decoration-none text-dark"
      >
        <div class="job-card card h-100 shadow-sm hover-shadow">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-3 text-center">
                <img
                  src="{{ vacancy.employer_logo_url }}"
                  alt="{{ vacancy.employer_name }} logo"
                  class="company-logo"
                />
              </div>
              <div class="col-9">
                <h5 class="card-title mb-1">{{ vacancy.vacancy_title }}</h5>
                <p class="text-muted mb-2">{{ vacancy.employer_name }}</p>

                <div class="d-flex gap-2 mb-3">
                  <span class="badge bg-light text-dark rounded-pill">
                    <i class="bi bi-globe me-1"></i> {{ vacancy.office_schedule }}
                  </span>
                  <span class="badge bg-light text-dark rounded-pill">
                    <i class="bi bi-geo-alt me-1"></i> {{ vacancy.vacancy_country }}
                  </span>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <span class="salary-highlight">
                      {{ vacancy.salary_currency }} {{ vacancy.salary_min }} -
                      {{ vacancy.salary_max }} <br />
                    </span>
                    <span class="text-muted">/month</span>
                  </div>
                  <small class="text-muted">
                    Posted {{ vacancy.vacancy_age }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a>
      {% endfor %}
    </div>
  </div>
</section>

<!-- Highest Paying Jobs Section -->
<section class="py-5 bg-light">
  <div class="container">
    <h2 class="fs-1 fw-bold border-bottom pb-3 mb-4">
      <i class="bi bi-graph-up me-3"></i>
      Top Paying Positions
    </h2>

    <div class="row g-4">
      {% for vacancy in highest_paying_jobs %}
      <a
        href="{{ url_for('view_vacancy', vacancy_id=vacancy.vacancy_id) }}"
        class="col-md-6 text-decoration-none text-dark"
      >
        <div class="job-card card h-100 shadow-sm hover-shadow">
          <div class="card-body">
            <div class="row align-items-center">
              <div class="col-3 text-center">
                <img
                  src="{{ vacancy.employer_logo_url }}"
                  alt="{{ vacancy.employer_name }} logo"
                  class="company-logo"
                />
              </div>
              <div class="col-9">
                <h5 class="card-title mb-1">{{ vacancy.vacancy_title }}</h5>
                <p class="text-muted mb-2">{{ vacancy.employer_name }}</p>

                <div class="d-flex gap-2 mb-3">
                  <span class="badge bg-light text-dark rounded-pill">
                    <i class="bi bi-globe me-1"></i> {{ vacancy.office_schedule }}
                  </span>
                  <span class="badge bg-light text-dark rounded-pill">
                    <i class="bi bi-geo-alt me-1"></i> {{ vacancy.vacancy_country }}
                  </span>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <span class="salary-highlight">
                      {{ vacancy.salary_currency }} {{ vacancy.salary_min }} -
                      {{ vacancy.salary_max }} <br />
                    </span>
                    <span class="text-muted">/month</span>
                  </div>
                  <small class="text-muted">
                    Posted {{ vacancy.vacancy_age }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </a>
      {% endfor %}
    </div>
  </div>
</section>

{% endblock %}
