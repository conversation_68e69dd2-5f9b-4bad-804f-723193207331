{% extends "base.html" %} {% block title %}Post Jobs on Our Portal{% endblock %}
{% block content %}
<div class="container py-5">
  <div class="row justify-content-center align-items-center">
    <div class="col-12 col-md-8 col-lg-6 d-none d-lg-block">
      <div class="rounded p-4 mb-4">
        <img
          src="./static/business-card.svg"
          alt="placeholder"
          class="img-fluid rounded mx-auto d-block"
        />
      </div>
    </div>

    <div class="col-12 col-md-8 col-lg-6">
      <div class="rounded shadow p-4 mb-4" style="background-color: white;">
        <h2 class="text-center mb-4">Post Jobs on Our Portal</h2>
        <p class="text-center text-muted mb-4">
          Register your company to post verified job opportunities. <br />
          All postings are free but subject to approval.
        </p>

        <form
          action="/employer-signup"
          method="post"
          enctype="multipart/form-data"
        >
          <!-- Company Name -->
          <div class="mb-4">
            <label for="companyName" class="form-label">Company Name</label>
            <input
              type="text"
              class="form-control"
              id="companyName"
              name="companyName"
              placeholder="Acme Corp"
              required
            />
          </div>

          <!-- Contact Person -->
          <div class="mb-4">
            <label for="contactName" class="form-label">Contact Person</label>
            <input
              type="text"
              class="form-control"
              id="contactName"
              name="contactName"
              placeholder="Jane Smith"
              required
            />
          </div>

          <!-- Email -->
          <div class="mb-4">
            <label for="email" class="form-label">Work Email</label>
            <input
              type="email"
              class="form-control"
              id="email"
              name="email"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <!-- Phone -->
          <div class="mb-4">
            <label for="phone" class="form-label">Contact Number</label>
            <input
              type="tel"
              class="form-control"
              id="phone"
              name="phone"
              placeholder="+****************"
              required
            />
          </div>

          <!-- Company Website -->
          <div class="mb-4">
            <label for="website" class="form-label">Company Website</label>
            <input
              type="url"
              class="form-control"
              id="website"
              name="website"
              placeholder="https://www.yourcompany.com"
              pattern="https?://.+"
              title="Please enter a valid URL starting with http:// or https://"
              onblur="if (this.value && !this.value.startsWith('http')) this.value = 'https://' + this.value"
              required
            />
          </div>

          <!-- Verification Questions -->
          <div class="mb-4">
            <label class="form-label">Verification Information</label>
            <textarea
              class="form-control"
              rows="3"
              placeholder="Please provide any information that will help us verify your company (e.g., LinkedIn profile, company registration number, etc.)"
              name="verificationInfo"
            ></textarea>
          </div>

          <!-- Agreement Checkbox -->
          <div class="mb-4 form-check">
            <input
              type="checkbox"
              class="form-check-input"
              id="agreement"
              name="agreement"
              required
              onchange="toggleSubmitButton()"
            />
            <label class="form-check-label" for="agreement">
              I agree to post only genuine job opportunities and provide
              accurate information
            </label>
          </div>

          <!-- Submit Button -->
          <div class="d-grid gap-2">
            <button
              type="submit"
              class="btn btn-primary btn-lg"
              id="submitBtn"
              disabled
            >
              Submit Registration
            </button>
          </div>
        </form>

        <div class="text-center mt-4">
          <small class="text-muted">
            We'll contact you within 2 business days to verify your company
          </small>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function toggleSubmitButton() {
    const agreementCheckbox = document.getElementById("agreement");
    const submitButton = document.getElementById("submitBtn");
    submitButton.disabled = !agreementCheckbox.checked;
  }
</script>
{% endblock %}
