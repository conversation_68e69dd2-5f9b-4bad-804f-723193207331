<!-- Jobs List Column -->
<div class="col-12 col-md-4 col-lg-3 mb-4 mb-md-0">
  <div
    id="jobs-small-list"
    class="rounded py-4 px-2 row shadow-sm mx-0"
    style="background-color: white"
  >
    <h5 class="text-start px-3"><strong>Related Jobs</strong></h5>
    <hr />
    <div
      id="sorted-jobs-short"
      class="col-12 overflow-auto"
      style="max-height: 60vh; height: max-content"
    >
      <div id="jobs-listed">
        {% if all_vacancies %}
        {% for vacancy in all_vacancies %}
        <div class="listing-item">
          <div
            id="button-{{vacancy.vacancy_id}}"
            class="row g-2 my-auto listing-pointer {% if loop.first %} active {% endif %}"
            onclick="switch_listing('button-{{vacancy.vacancy_id}}','listing-div-{{vacancy.vacancy_id}}')"
          >
            <div class="col-3 col-md-2">
              <img
                src="{{vacancy.employer_logo_url}}"
                class="img-fluid rounded employer-icon-sm"
                alt="Job Icon"
                style="max-width: 40px"
              />
            </div>
            <div class="col-9 col-md-10">
              <h6 class="mb-1">{{vacancy.vacancy_title}}</h6>
              <p class="mb-0">
                <small class="text-muted">
                  {{vacancy.employer_name}}<br />
                  {% if vacancy.vacancy_city %} {{vacancy.vacancy_city}}, {% endif %} {{vacancy.vacancy_country}}
                </small>
              </p>
            </div>
          </div>
          <hr class="my-2" />
        </div>
        {% endfor %}
        {% else %}
        <div class="text-center mt-5">
          <h5>Sorry, it seems that there exists no jobs that fit your search query</h5>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Job Details Column -->
<div class="col-12 col-md-8 col-lg-9">
  {% if all_vacancies %}
  {% for vacancy in all_vacancies %}
  <div
    id="listing-div-{{vacancy.vacancy_id}}"
    class="rounded listing shadow-sm {% if loop.first %} visible {% endif %}"
  >
    <div
      class="rounded-top w-100"
      style="
        height: 120px;
        background-image: url('{{vacancy.employer_banner_url}}');
        background-size: cover;
        background-position: center;
        {% if vacancy.listing_source == 'WeWorkRemotely' %}
        background-image: radial-gradient(circle at 50% 50%, #353535, #2a2a2a, #1f1f1f, #141414, #0f0f0f, #080808, #000000);
        {% endif %}
      "
    ></div>

    <div class="row g-3 m-2 m-md-4">
      <!-- Job content section -->
      <div class="col-12 col-lg-8 border-lg-end mb-4 mb-lg-0">
        <!-- Job header -->
        <div class="row align-items-center mb-4">
          <div class="col-4 col-sm-3 col-md-2 p-2">
            <img
              src="{{vacancy.employer_logo_url}}"
              class="img-fluid rounded employer-icon-md"
              alt="Job Icon"
              style="max-width: 80px"
            />
          </div>

          <div class="col-8 col-sm-9 col-md-10">
            <h3 class="h4 h2-md">{{vacancy.vacancy_title}}</h3>
            <p class="mb-0">
              <small class="text-muted"> {{vacancy.employer_name}} </small>
            </p>
          </div>
        </div>

        <hr class="my-2 d-none d-md-block" />

        <div name="job-desc" class="p-2">{{ vacancy.vacancy_job_description|safe }}</div>
      </div>

      <!-- Job details sidebar -->
      <div class="col-12 col-lg-4 ps-lg-4">
        <div name="quick-facts" class="mb-4">
          <h6 class="h5">Position Facts</h6>
          <div class="mt-3">
            <p class="mb-3">
              <strong>Monthly Salary Budget:</strong><br />
              {{vacancy.salary_min}} - {{vacancy.salary_max}}
              {{vacancy.salary_currency}}
            </p>
            <p class="mb-3">
              <strong>Work Mode:</strong><br />
              {{vacancy.office_schedule}}
            </p>
            <p class="mb-3">
              <strong>Work Location:</strong><br />
              {% if vacancy.vacancy_city %} {{vacancy.vacancy_city}}, {% endif %} {{vacancy.vacancy_country}}
            </p>
          </div>
        </div>

        <hr class="my-2" />

        {% if all_tags.get(vacancy.vacancy_id) and all_tags[vacancy.vacancy_id][0] %}
        <div name="highlight-badges" class="my-4">
          <h6 class="h5">Key Advantages</h6>
          <div class="d-flex flex-wrap gap-2 mt-3">
            {% for tag in all_tags[vacancy.vacancy_id] %}
            <span class="badge bg-dark text-wrap p-2 m-0">
              <small>{{tag}}</small>
            </span>
            {% endfor %}
          </div>
        </div>

        <hr class="my-2" />
        {% endif %}

        <div name="apply-now" class="mt-4">
          <div class="d-grid gap-2">
            {% if vacancy.listing_source == "Canvider" %}
            <a
              href="{{url_for('view_vacancy',vacancy_id=vacancy.vacancy_id)}}"
              target="_blank"
              class="btn btn-outline-dark btn-lg mt-1 mb-3 shadow-sm"
            >
              Apply Now <i class="bi bi-arrow-up-right-square-fill"></i>
            </a>
            {% else %}
            <a
              href="{{vacancy.vacancy_url}}"
              target="_blank"
              class="btn btn-outline-dark btn-lg my-1 shadow-sm"
            >
              Apply Now <i class="bi bi-arrow-up-right-square-fill"></i>
            </a>
            {% endif %}

            {% if vacancy.listing_source == "Canvider" %}
            <a
              href="{{url_for('employer',employer_id=vacancy.employer_id)}}"
              target="_blank"
              class="btn btn-outline-dark btn-lg mt-1 mb-3 shadow-sm"
            >
              Company Page <i class="bi bi-arrow-up-right-square-fill"></i>
            </a>
            {% else %}
            <a
              href="{{vacancy.company_url}}"
              target="_blank"
              class="btn btn-outline-dark btn-lg mt-1 mb-3 shadow-sm"
            >
              Company Page <i class="bi bi-arrow-up-right-square-fill"></i>
            </a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endfor %}
  {% endif %}
</div>
